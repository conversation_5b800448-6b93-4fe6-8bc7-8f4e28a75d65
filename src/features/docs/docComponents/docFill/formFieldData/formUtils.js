import { agreementtoRevive } from "./Colorado/agreementtoRevive";
import { assignmentofLease } from "./Colorado/assignmentofLease";
import { amendExtendContract } from "./Colorado/amendExtendContract";
import { appraisalValueObjectionNotice } from "./Colorado/appraisalValueObjectionNotice";
import { billofSaleContract } from "./Colorado/billofSaleContract";
import { billofSalePPA } from "./Colorado/billofSalePPA";
import { brokerageDisclosuretoBuyer } from "./Colorado/brokerageDisclosuretoBuyer";
import { brokerageDisclosuretoSellerFSBO } from "./Colorado/brokerageDisclosuretoSellerFSBO";
import { brokerageDutiesDisclosuretoSellernonCRECListing } from "./Colorado/brokerageDutiesDisclosuretoSellernonCRECListing";
import { cBColoradoAffiliatedBusinessDisclosure } from "./Colorado/cBColoradoAffiliatedBusinessDisclosure";
import { cBMoldDisclosure } from "./Colorado/cBMoldDisclosure";
import { cBSalesAssociateElectionPersonalTransactions } from "./Colorado/cBSalesAssociateElectionPersonalTransactions";
import { cBRadonDisclosure } from "./Colorado/cBRadonDisclosure";
import { cBWireFraudDisclosure } from "./Colorado/cBWireFraudDisclosure";
import { changeofStatus } from "./Colorado/changeofStatus";
import { contracttoBuyandSellCommercial } from "./Colorado/contracttoBuyandSellCommercial";
import { contracttoBuyandSellIncome } from "./Colorado/contracttoBuyandSellIncome";
import { contracttoBuyandSellLand } from "./Colorado/contracttoBuyandSellLand";
import { contracttoBuyandSellResidential } from "./Colorado/contracttoBuyandSellResidential";
import { counterproposal } from "./Colorado/counterproposal";
import { cOVID19Addendum2020 } from "./Colorado/cOVID19Addendum2020";
import { definitionsofWorkingRelationshipsForBuyers } from "./Colorado/definitionsofWorkingRelationshipsForBuyers";
import { definitionsofWorkingRelationshipsForSellers } from "./Colorado/definitionsofWorkingRelationshipsForSellers";
import { earnestMoneyReceipt } from "./Colorado/earnestMoneyReceipt";
import { earnestMoneyRelease } from "./Colorado/earnestMoneyRelease";
import { estoppelStatement } from "./Colorado/estoppelStatement";
import { exchangeAddendumtoContracttoBuySellRealEstate } from "./Colorado/exchangeAddendumtoContracttoBuySellRealEstate";
import { exclusiveRighttoBuy } from "./Colorado/exclusiveRighttoBuy";
import { exclusiveRighttoSell } from "./Colorado/exclusiveRighttoSell";
import { extensionorTermination } from "./Colorado/extensionorTermination";
import { greenDisclosure } from "./Colorado/greenDisclosure";
import { inspectionObjectionNotice } from "./Colorado/inspectionObjectionNotice";
import { inspectionResolution } from "./Colorado/inspectionResolution";
import { leadBasedPaintDisclosureSalesWritable1 } from "./Colorado/leadBasedPaintDisclosureSalesWritable1";
import { listingContractAmendExtendCleanWritable2020 } from "./Colorado/listingContractAmendExtendCleanWritable2020";
import { noticeToTerminate } from "./Colorado/noticeToTerminate";
import { openListingAddendumtoListingContract } from "./Colorado/openListingAddendumtoListingContract";
import { personalPropertyAgreement } from "./Colorado/personalPropertyAgreement";
import { postClosingOccupancyAgreement } from "./Colorado/postClosingOccupancyAgreement";
import { residentialAddendumtoContracttoBuySellRealEstate } from "./Colorado/residentialAddendumtoContracttoBuySellRealEstate";
import { sellersPropertyDisclosureLand } from "./Colorado/sellersPropertyDisclosureLand";
import { sellersPropertyDisclosureResidential } from "./Colorado/sellersPropertyDisclosureResidential";
import { shortSaleAddendumCBS } from "./Colorado/shortSaleAddendumCBS";
import { shortSaleAddendumSellerListingContract } from "./Colorado/shortSaleAddendumSellerListingContract";
import { sourceOfWaterAddendum } from "./Colorado/sourceOfWaterAddendum";
import { squareFootageDisclosure } from "./Colorado/squareFootageDisclosure";
import { titleRelatedObjectionNotice } from "./Colorado/titleRelatedObjectionNotice";
import { rEMAXWireFraudAlert } from "./Colorado/rEMAXWireFraudAlert";
import { contracttoBuyandSellResidential2023 } from "./Colorado/contracttoBuyandSellResidential2023";
import { cBExclusiveRighttoSell2023 } from "./Colorado/cBExclusiveRighttoSell2023";
import { cBExclusiveRighttoBuy2023 } from "./Colorado/cBExclusiveRighttoBuy2023";
import { sellersPropertyDisclosureResidential2023 } from "./Colorado/sellersPropertyDisclosureResidential2023";
import { contracttoBuyandSellIncomeResidential2023 } from "./Colorado/contracttoBuyandSellIncome2023";
import { contracttoBuyandSellForeclosure2023 } from "./Colorado/contracttoBuyandSellForeclosure2023";
import { rBSellerComingSoonAddendum2022 } from "./Colorado/rBSellerComingSoonAddendum2022";
import { rBCommissionsDisclosure1023 } from "./Colorado/rBCommissionsDisclosure1023";
import { rBBrokerRebatetoBuyer2022 } from "./Colorado/rBBrokerRebatetoBuyer2022";
import { rBHomeInspectionWaiverAgreement23 } from "./Colorado/rBHomeInspectionWaiverAgreement23";
import { closingInstructions } from "./Colorado/closingInstructions";
import { cBCExclusiveRighttoBuy2023 } from "./Colorado/cBCExclusiveRighttoBuy2023";
import { cBCExclusiveRighttoSell2023 } from "./Colorado/cBCExclusiveRighttoSell2023";
import { cBDPAffiliatedBusinesses2022 } from "./Colorado/cBDPAffiliatedBusinesses2022";
import { homeWarrantyAHS2023 } from "./Colorado/homeWarrantyAHS2023";
import { exclusiveRighttoSell2024 } from "./Colorado/exclusiveRighttoSell2024";
import { exclusiveRighttoBuy2023 } from "./Colorado/exclusiveRighttoBuy2023";
import { sellersPropertyDisclosuresLand2024 } from "./Colorado/sellersPropertyDisclosureLand2024";
import { homeWarrantyInspectionFormHSPR } from "./Colorado/homeWarrantyInspectionFormHSPR";
import { fSBOCommissionAgreementHSPR } from "./Colorado/fSBOCommissionAgreementHSPR";
import { referralAgreementReceivingAgentHSPR } from "./Colorado/referralAgreementReceivingAgentHSPR";
import { referralAgreementOriginatingAgentHSPR } from "./Colorado/referralAgreementOriginatingAgentHSPR";
import { listingTerminationAgreementHSPR } from "./Colorado/listingTerminationAgreementHSPR";
import { listingContractAmendExtend2023 } from "./Colorado/listingContractAmendExtend2023";
import { counterproposal2023 } from "./Colorado/counterproposal2023";
import { buyerContractAmendExtend2023 } from "./Colorado/buyerContractAmendExtend2023";
import { inspectionObjectionNotice2023 } from "./Colorado/inspectionObjectionNotice2023";
import { amendExtendContract2023 } from "./Colorado/amendExtendContract2023";
import { amendExtendWithoutDates } from "./Colorado/amendExtendWithoutDates";
import { contracttoBuyandSellLand2024 } from "./Colorado/contracttoBuyandSellLand2024";
import { exclusiveRighttoSell2024CB } from "./Colorado/exclusiveRighttoSell2024CB";
import { residentialAddendumtoContract2024 } from "./Colorado/residentialAddendumtoContract2024";
import { energyBenchmarkingDisclosure } from "./Colorado/energyBenchmarkingDisclosure";
import { noticetoTerminateSeller } from "./Colorado/noticetoTerminateSeller";
import { noticetoTerminateBuyer } from "./Colorado/noticetoTerminateBuyer";
import { inspectionResolution2023 } from "./Colorado/inspectionResolution2023";
import { sellersPropertyDisclosureSupplementAdditionalStructure } from "./Colorado/sellersPropertyDisclosureSupplementAdditionalStructure";
import { affiliatedBusinessDisclosureCBDP } from "./Colorado/affiliatedBusinessDisclosureCBDP";
import { addendumBlank } from "./Colorado/addendumBlank";
import { affiliateBusinessArrangementHSPR } from "./Colorado/affiliateBusinessArrangementHSPR";
import { manufacturedHomeAddendum2024 } from "./Colorado/manufacturedHomeAddendum2024";
import { manufacturedHomeAmendExtend2024 } from "./Colorado/manufacturedHomeAmendExtend2024";
import { manufacturedHomeCBSLotLease2024 } from "./Colorado/manufacturedHomeCBSLotLease2024";
import { manufacturedHomeCounterproposal2024 } from "./Colorado/manufacturedHomeCounterproposal2024";
// import { onetoFourFamilyResidentialContract } from "./Texas/oneToFourFamilyResidentialContract";
// import { farmandRanchContract } from "./Texas/farmandRanchContract";
// import { noticetoPurchaserofSpecialTaxingDistrict } from "./Texas/noticetoPurchaserofSpecialTaxingDistrict";
// import { residentialCondominiumContract } from "./Texas/residentialCondominiumContract";
// import { unimprovedPropertyContract } from "./Texas/unimprovedPropertyContract";
// import { subdivisionandPropertyOwnersAssociation } from "./Texas/subdivisionandPropertyOwnersAssociation";
// import { sellersTemporaryResidentialLease } from "./Texas/sellersTemporaryResidentialLease";
// import { addendumRegardingResidentialLeases } from "./Texas/addendumRegardingResidentialLeases";
// import { noticeofBuyersTermination } from "./Texas/noticeofBuyersTermination";
// import { condominiumResaleCertificate } from "./Texas/condominiumResaleCertificate";
// import { addendumforCoastalAreaProperty } from "./Texas/addendumCoastalAreaProperty";
// import { addendumforBackUpContract } from "./Texas/addendumBackUpContract";
// import { addendumforAuthorizingHydrostaticTesting } from "./Texas/addendumAuthorizingHydrostaticTesting";
// import { addendumforPropaneGasArea } from "./Texas/addendumPropaneGasArea";
// import { addendumforMandatoryMembershipinOwnersAssociation } from "./Texas/addendumMandatoryMembershipinOwnersAssociation";
// import { addendumforReservationofOilGasMinerals } from "./Texas/addendumReservationofOilGasMinerals";
// import { addendumforSaleofOtherPropertybyBuyer } from "./Texas/addendumSaleofOtherPropertybyBuyer";
// import { nonRealtyItemsAddendum } from "./Texas/addendumNonRealtyItems";
// import { addendumforLeadBasedPaint } from "./Texas/addendumLeadBasedPaint";
// import { loanAssumptionAddendum } from "./Texas/addendumLoanAssumption";
// import {
//   addendumShortSale,
//   shortSaleAddendum,
// } from "./Texas/addendumShortSale";
// import { landlordFloodplainandFloodNotice } from "./Texas/landlordFloodplainandFloodNotice";
// import {
//   addendumEnvironmentalAssessment,
//   environmentalAssessmentAddendum,
// } from "./Texas/addendumEnvironmentalAssessment";
// import { addendumRegardingFixtureLeases } from "./Texas/addendumRegardingFixtureLeases";
// import { buyersTemporaryResidentialLease } from "./Texas/buyersTemporaryResidentialLease";
// import { addendumforReleaseofLiabilityonAssumedLoan } from "./Texas/addendumReleaseofLiabilityonAssumedLoan";
// import { sellerFinancingAddendum } from "./Texas/addendumSellerFinancing";
// import {
//   addendumThirdPartyFinancing,
//   thirdPartyFinancingAddendum,
// } from "./Texas/addendumThirdPartyFinancing";
// import { newHomeContractCompletedConstruction } from "./Texas/newHomeContractCompletedConstruction";
// import { newHomeContractIncompleteConstruction } from "./Texas/newHomeContractIncompleteConstruction";
//Oklahoma
// import { 201311cfpbkbyoclosingdisclosure } from "./201311cfpbkbyoclosingdisclosure";
import { accessoriesSupplement } from "./Oklahoma/accessoriesSupplement";
import { acknowledgmentandConfirmationofDisclosures } from "./Oklahoma/acknowledgmentandConfirmationofDisclosures";
import { affidavitforTitleorOwnershipofLandIndividual } from "./Oklahoma/affidavitforTitleorOwnershipofLandIndividual";
import { appendixAResidentialPropertyCondition } from "./Oklahoma/appendixAResidentialPropertyCondition";
import { appendixBRPCDisclaimer } from "./Oklahoma/appendixBRPCDisclaimer";
import { backUpSupplement } from "./Oklahoma/backUpSupplement";
import { blankAddendum } from "./Oklahoma/blankAddendum";
import { brokerServicesInformation } from "./Oklahoma/brokerServicesInformation";
import { buyerBrokerServiceAgreement } from "./Oklahoma/buyerBrokerServiceAgreement";
import { buyersCounteroffertoSellersCounteroffer } from "./Oklahoma/buyersCounteroffertoSellersCounteroffer";
import { buyersRemovalofConditionNotice } from "./Oklahoma/buyersRemovalofConditionNotice";
import { conditionedonSalePresentlyUnderContract } from "./Oklahoma/conditionedonSalePresentlyUnderContract";
import { condominiumAssociation } from "./Oklahoma/condominiumAssociation";
import { conventionalLoan } from "./Oklahoma/conventionalLoan";
import { disclosureofBrokerageServiceBuyer } from "./Oklahoma/disclosureofBrokerageServiceBuyer";
import { disclosureofBrokerageServiceSeller } from "./Oklahoma/disclosureofBrokerageServiceSeller";
import { disclosureofFamilialandBeneficialInterest } from "./Oklahoma/disclosureofFamilialandBeneficialInterest";
import { earnestMoneyForm } from "./Oklahoma/earnestMoneyForm";
import { escalationAddendum } from "./Oklahoma/escalationAddendum";
import { fHALoan } from "./Oklahoma/fHALoan";
import { farmRanchRecConventionalLoan } from "./Oklahoma/farmRanchRecConventionalLoan";
import { farmRanchRecLandExhibitA } from "./Oklahoma/farmRanchRecLandExhibitA";
import { farmRanchRecLandExhibitB } from "./Oklahoma/farmRanchRecLandExhibitB";
import { farmRanchRecLandRemovalofLivestock } from "./Oklahoma/farmRanchRecLandRemovalofLivestock";
import { farmandRanch } from "./Oklahoma/farmandRanch";
import { forYourProtectionGetaHomeInspection } from "./Oklahoma/forYourProtectionGetaHomeInspection";
import { landWithorWithoutDwelling } from "./Oklahoma/landWithorWithoutDwelling";
import { leadBasedPaintSellerDisclosure } from "./Oklahoma/leadBasedPaintSellerDisclosure";
import { legalDescriptionSupplement } from "./Oklahoma/legalDescriptionSupplement";
import { licenseeDisclosureRPCD } from "./Oklahoma/licenseeDisclosureRPCD";
import { listingAgreementAmendorExtend } from "./Oklahoma/listingAgreementAmendorExtend";
import { listingAgreement } from "./Oklahoma/listingAgreement";
import { nativeAmericanHomeLoan } from "./Oklahoma/nativeAmericanHomeLoan";
import { newHomeConstruction } from "./Oklahoma/newHomeConstruction";
import { noticeofCancellation } from ".Oklahoma/noticeofCancellation";
import { noticeofTRR } from "./Oklahoma/noticeofTRR";
import { propertyDataForm } from "./Oklahoma/propertyDataForm";
import { rPCDExemptionForm } from "./Oklahoma/rPCDExemptionForm";
import { releaseofContractandDisbursementofEarnestMoney } from "./Oklahoma/releaseofContractandDisbursementofEarnestMoney";
import { residentialListingExclusiveRighttoLease } from "./Oklahoma/residentialListingExclusiveRighttoLease";
import { residentialPropertyDisclosureAct012014 } from "./Oklahoma/residentialPropertyDisclosureAct012014";
import { residentialSale } from "./Oklahoma/residentialSale";
import { saleofBuyersPropertyConditionNotUnderContract } from "./Oklahoma/saleofBuyersPropertyConditionNotUnderContract";
import { sellersConditionRemovalDemandNotification } from "./Oklahoma/sellersConditionRemovalDemandNotification";
import { sellersCounteroffer } from "./Oklahoma/sellersCounteroffer";
import { singleFamilyHomeownersAssociation } from "./Oklahoma/singleFamilyHomeownersAssociation";
import { standardClauses } from "./Oklahoma/standardClauses";
import { supplement } from "./Oklahoma/supplement";
import { townhouseAssociation } from "./Oklahoma/townhouseAssociation";
import { uSDARuralHousingLoan } from "./Oklahoma/uSDARuralHousingLoan";
import { vALoan } from "./Oklahoma/vALoan";
import { vacantLotorTract } from "./Oklahoma/vacantLotorTract";
import { wireAdvisory } from "./Oklahoma/wireAdvisory";
import { counterproposalWithoutDates } from "./Colorado/counterproposalWithoutDates";
import { assumptionofLoan } from "./Oklahoma/assumptionofLoan";
import { cBFSBOAgreementforPaymentofCommission } from "./Colorado/cBFSBOAgreementforPaymentofCommission";
import { brokerageFirmCompensationAgreement } from "./Colorado/brokerageFirmCompensationAgreement";
import { contracttoBuyandSellResidential2024 } from "./Colorado/contracttoBuyandSellResidential2024";
import { brokerageDisclosuretoBuyer2024 } from "./Colorado/brokerageDisclosuretoBuyer2024";

import { contracttoBuyandSellLand2024Aug } from "./Colorado/contracttoBuyandSellLand2024Aug";
import { exclusiveRighttoBuyCB2024Aug } from "./Colorado/exclusiveRighttoBuyCB2024Aug";
import { exclusiveRighttoSellCB2024Aug } from "./Colorado/exclusiveRighttoSellCB2024Aug";
import { exclusiveRighttoSell2024Aug } from "./Colorado/exclusiveRighttoSell2024Aug";
import { exclusiveRighttoBuy2024Aug } from "./Colorado/exclusiveRighttoBuy2024Aug";
import { contracttoBuyandSellCommercial2024 } from "./Colorado/contracttoBuyandSellCommercial2024";
import { contracttoBuyandSellForeclosure2024 } from "./Colorado/contracttoBuyandSellForeclosure2024";
import { additionalDesignatedBrokerDC616 } from "./Colorado/additionalDesignatedBrokerDC616";
import { buyerAdvisoryResidentialDC924 } from "./Colorado/buyerAdvisoryResidentialDC924";
import { sellerAdvisoryResidentialDC924 } from "./Colorado/sellerAdvisoryResidentialDC924";
import { terminationAgreementBuyerDC523 } from "./Colorado/terminationAgreementBuyerDC523";
import { terminationAgreementSellerDC523 } from "./Colorado/terminationAgreementSellerDC523";
import { disclosureofBuyerBrokerCompensation92324 } from "./Colorado/disclosureofBuyerBrokerCompensation92324";
import { exclusiveRighttoSellCB202409 } from "./Colorado/exclusiveRighttoSellCB202409";
import { sellersNetSheet } from "./Colorado/sellersNetSheet";
import { sellersOffertoBuyersBroker } from "./Colorado/sellersOffertoBuyersBroker";
import { exclusiveRighttoBuyCB2025 } from "./Colorado/exclusiveRighttoBuyCB2025";
import { exclusiveRighttoSellCB2025 } from "./Colorado/exclusiveRighttoSellCB2025";
import { realBrokerageAffiliatedBusinessDisclosure } from "./Colorado/realBrokerageAffiliatedBusinessDisclosure";
import { realBrokerageWireFraudDisclosure } from "./Colorado/realBrokerageWireFraudDisclosure";
import { commissionDisbursementAuthorization } from "./Colorado/commissionDisbursementAuthorization";
import {  uTILITYINFOFORM2025GALLES } from "./Colorado/uTILITYINFOFORM2025GALLES";
import {  aDMINClosingWorksheetGalles } from "./Colorado/aDMINClosingWorksheetGalles";
import { greenSheetGalles } from "./Colorado/greenSheetGalles";
import { terminationofMarketingEffortsFRASCONA } from "./Colorado/terminationofMarketingEffortsFRASCONA";
import { listingTerminationAgreementFRASCONA } from "./Colorado/listingTerminationAgreementFRASCONA";
import { sellerAddendum2022FRASCONA } from "./Colorado/sellerAddendum2022FRASCONA";
import { mLSCRENDelayedEntryForm } from "./Colorado/mLSCRENDelayedEntryForm";
import { buyerAdvisoryVacantLandDC924 } from "./Colorado/buyerAdvisoryVacantLandDC924";
import { sellerAdvisoryVacantLandDC924 } from "./Colorado/sellerAdvisoryVacantLandDC924";
import { coListingAddendumBuyerDC120 } from "./Colorado/coListingAddendumBuyerDC120";
import { wIREFRAUDWARNINGLillardRealtyGroup } from "./Colorado/wIREFRAUDWARNINGLillardRealtyGroup";
import { aFFILIATEDBUSINESSDISCLOSURERR } from "./Colorado/aFFILIATEDBUSINESSDISCLOSURERR";
import { wIREFRAUDRR } from "./Colorado/wIREFRAUDRR";



function compareFontAndHeight(formFieldData, title) {
  formFieldData.forEach((field) => {
    if (field.height && field.fontSize) {
      if (field.fontSize >= field.height) {
      }
    }
  });
}

export function checkFontSizeGtHeight() {
  let formFieldData = [];
  let title = "";
  //  title = "Buyer Agreement Form Long";
  //       formFieldData = buyerAgreementFormLong();
  //       compareFontAndHeight(formFieldData, title);
  title = "AFFILIATED BUSINESS DISCLOSURE RR";
        formFieldData = aFFILIATEDBUSINESSDISCLOSURERR();
        compareFontAndHeight(formFieldData, title);
  title = "WIRE FRAUD RR";
        formFieldData = wIREFRAUDRR();
        compareFontAndHeight(formFieldData, title);
   title = "WIRE FRAUD WARNING Lillard Realty Group";
        formFieldData = wIREFRAUDWARNINGLillardRealtyGroup();
        compareFontAndHeight(formFieldData, title);
  title = "Co-Listing Addendum - Buyer (DC-1-20)";
        formFieldData = coListingAddendumBuyerDC120();
        compareFontAndHeight(formFieldData, title);
  title = "Seller Advisory (Vacant Land) (DC-9-24)";
        formFieldData = sellerAdvisoryVacantLandDC924();
        compareFontAndHeight(formFieldData, title);
  title = "Buyer Advisory (Vacant Land) (DC-9-24)";
        formFieldData = buyerAdvisoryVacantLandDC924();
        compareFontAndHeight(formFieldData, title);
  title = "MLS CREN Delayed Entry Form";
        formFieldData = mLSCRENDelayedEntryForm();
        compareFontAndHeight(formFieldData, title);
  title = "Seller Addendum 2022 FRASCONA";
        formFieldData = sellerAddendum2022FRASCONA();
        compareFontAndHeight(formFieldData, title);
  title = "Listing Termination Agreement FRASCONA";
        formFieldData = listingTerminationAgreementFRASCONA();
        compareFontAndHeight(formFieldData, title);
title = "Termination of Marketing Efforts FRASCONA";
        formFieldData = terminationofMarketingEffortsFRASCONA();
        compareFontAndHeight(formFieldData, title);
   title = "Commission Disbursement Authorization";
        formFieldData = commissionDisbursementAuthorization();
  compareFontAndHeight(formFieldData, title);
 
  title = "Green Sheet Galles";
        formFieldData = greenSheetGalles();
        compareFontAndHeight(formFieldData, title);
   title = "ADMIN Closing Worksheet Galles";
        formFieldData = aDMINClosingWorksheetGalles();
        compareFontAndHeight(formFieldData, title);
  title = "UTILITY INFO FORM 2025 GALLES";
        formFieldData = uTILITYINFOFORM2025GALLES();
        compareFontAndHeight(formFieldData, title);
      title = "Real Brokerage - Wire Fraud Disclosure";
        formFieldData = realBrokerageWireFraudDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "Real Brokerage - Affiliated Business Disclosure";
        formFieldData = realBrokerageAffiliatedBusinessDisclosure();
        compareFontAndHeight(formFieldData, title);

  title = "Exclusive Right to Sell CB 2025";
        formFieldData = exclusiveRighttoSellCB2025();
        compareFontAndHeight(formFieldData, title);
   title = "Exclusive Right to Buy CB 2025";
        formFieldData = exclusiveRighttoBuyCB2025();
        compareFontAndHeight(formFieldData, title);
  title = "Sellers Offer to Buyers Broker";
        formFieldData = sellersOffertoBuyersBroker();
        compareFontAndHeight(formFieldData, title);
  title = "Seller Net Sheet";
        formFieldData = sellersNetSheet();
        compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Sell CB 2024-09";
        formFieldData = exclusiveRighttoSellCB202409();
        compareFontAndHeight(formFieldData, title);
   title = "Disclosure of Buyer Broker Compensation 9-23-24";
        formFieldData = disclosureofBuyerBrokerCompensation92324();
        compareFontAndHeight(formFieldData, title);
   title = "Termination Agreement - Seller (DC-5-23)";
        formFieldData = terminationAgreementSellerDC523();
        compareFontAndHeight(formFieldData, title);
  title = "Termination Agreement - Buyer (DC-5-23)";
        formFieldData = terminationAgreementBuyerDC523();
        compareFontAndHeight(formFieldData, title);
  title = "Seller Advisory (Residential) (DC-9-24)";
        formFieldData = sellerAdvisoryResidentialDC924();
        compareFontAndHeight(formFieldData, title);
  title = "Buyer Advisory (Residential) (DC-9-24)";
        formFieldData = buyerAdvisoryResidentialDC924();
        compareFontAndHeight(formFieldData, title);
   title = "Additional Designated Broker (DC-6-16)";
        formFieldData = additionalDesignatedBrokerDC616();
        compareFontAndHeight(formFieldData, title);
   title = "Contract to Buy and Sell, Foreclosure 2024";
        formFieldData = contracttoBuyandSellForeclosure2024();
        compareFontAndHeight(formFieldData, title);
title = "Contract to Buy and Sell, Commercial 2024";
        formFieldData = contracttoBuyandSellCommercial2024();
        compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Buy 2024 Aug";
        formFieldData = exclusiveRighttoBuy2024Aug();
        compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Sell 2024 Aug";
        formFieldData = exclusiveRighttoSell2024Aug();
        compareFontAndHeight(formFieldData, title);
   title = "Exclusive Right to Sell CB 2024 Aug";
        formFieldData = exclusiveRighttoSellCB2024Aug();
        compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Buy CB 2024 Aug";
        formFieldData = exclusiveRighttoBuyCB2024Aug();
        compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Land 2024 Aug";
        formFieldData = contracttoBuyandSellLand2024Aug();
        compareFontAndHeight(formFieldData, title);

  title = "Brokerage Disclosure to Buyer 2024";
        formFieldData = brokerageDisclosuretoBuyer2024();
        compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Residential 2024";
        formFieldData = contracttoBuyandSellResidential2024();
        compareFontAndHeight(formFieldData, title);
   title = "Brokerage Firm Compensation Agreement";
        formFieldData = brokerageFirmCompensationAgreement();
        compareFontAndHeight(formFieldData, title);
  
   title = "CB FSBO Agreement for Payment of Commission";
        formFieldData = cBFSBOAgreementforPaymentofCommission();
        compareFontAndHeight(formFieldData, title);
       title = "Assumption of Loan";
        formFieldData = assumptionofLoan();
        compareFontAndHeight(formFieldData, title);
      // title = "201311_cfpb_kbyo_closing-disclosure";
      //   formFieldData = 201311cfpbkbyoclosingdisclosure();
      //   compareFontAndHeight(formFieldData, title);
      title = "Accessories Supplement";
        formFieldData = accessoriesSupplement();
        compareFontAndHeight(formFieldData, title);
      title = "Acknowledgment and Confirmation of Disclosures";
        formFieldData = acknowledgmentandConfirmationofDisclosures();
        compareFontAndHeight(formFieldData, title);
     
      title = "Affidavit for Title or Ownership of Land - Individual";
        formFieldData = affidavitforTitleorOwnershipofLandIndividual();
        compareFontAndHeight(formFieldData, title);
      title = "Appendix A Residential Property Condition";
        formFieldData = appendixAResidentialPropertyCondition();
        compareFontAndHeight(formFieldData, title);
      title = "Appendix B RPC Disclaimer ";
        formFieldData = appendixBRPCDisclaimer();
        compareFontAndHeight(formFieldData, title);
      title = "Back-Up Supplement";
        formFieldData = backUpSupplement();
        compareFontAndHeight(formFieldData, title);
      title = "Blank Addendum";
        formFieldData = blankAddendum();
        compareFontAndHeight(formFieldData, title);
      title = "Broker Services Information";
        formFieldData = brokerServicesInformation();
        compareFontAndHeight(formFieldData, title);
      title = "Buyer Broker Service Agreement";
        formFieldData = buyerBrokerServiceAgreement();
        compareFontAndHeight(formFieldData, title);
      title = "Buyers Counteroffer to Sellers Counteroffer";
        formFieldData = buyersCounteroffertoSellersCounteroffer();
        compareFontAndHeight(formFieldData, title);
      title = "Buyers Removal of Condition Notice";
        formFieldData = buyersRemovalofConditionNotice();
        compareFontAndHeight(formFieldData, title);
      title = "Conditioned on Sale - Presently Under Contract ";
        formFieldData = conditionedonSalePresentlyUnderContract();
        compareFontAndHeight(formFieldData, title);
      title = "Condominium Association ";
        formFieldData = condominiumAssociation();
        compareFontAndHeight(formFieldData, title);
      title = "Conventional Loan";
        formFieldData = conventionalLoan();
        compareFontAndHeight(formFieldData, title);
      title = "Disclosure of Brokerage Service - Buyer";
        formFieldData = disclosureofBrokerageServiceBuyer();
        compareFontAndHeight(formFieldData, title);
      title = "Disclosure of Brokerage Service - Seller";
        formFieldData = disclosureofBrokerageServiceSeller();
        compareFontAndHeight(formFieldData, title);
      title = "Disclosure of Familial and Beneficial Interest";
        formFieldData = disclosureofFamilialandBeneficialInterest();
        compareFontAndHeight(formFieldData, title);
      title = "Earnest Money Form";
        formFieldData = earnestMoneyForm();
        compareFontAndHeight(formFieldData, title);
      title = "Escalation Addendum";
        formFieldData = escalationAddendum();
        compareFontAndHeight(formFieldData, title);
      title = "FHA Loan";
        formFieldData = fHALoan();
        compareFontAndHeight(formFieldData, title);
      title = "Farm Ranch Rec Conventional Loan";
        formFieldData = farmRanchRecConventionalLoan();
        compareFontAndHeight(formFieldData, title);
      title = "Farm Ranch Rec Land Exhibit A";
        formFieldData = farmRanchRecLandExhibitA();
        compareFontAndHeight(formFieldData, title);
      title = "Farm Ranch Rec Land Exhibit B";
        formFieldData = farmRanchRecLandExhibitB();
        compareFontAndHeight(formFieldData, title);
      title = "Farm Ranch Rec Land Removal of Livestock";
        formFieldData = farmRanchRecLandRemovalofLivestock();
        compareFontAndHeight(formFieldData, title);
      title = "Farm and Ranch";
        formFieldData = farmandRanch();
        compareFontAndHeight(formFieldData, title);
      title = "For Your Protection Get a Home Inspection";
        formFieldData = forYourProtectionGetaHomeInspection();
        compareFontAndHeight(formFieldData, title);
      title = "Land With or Without Dwelling";
        formFieldData = landWithorWithoutDwelling();
        compareFontAndHeight(formFieldData, title);
      title = "Lead-Based Paint Seller Disclosure";
        formFieldData = leadBasedPaintSellerDisclosure();
        compareFontAndHeight(formFieldData, title);
      title = "Legal Description Supplement";
        formFieldData = legalDescriptionSupplement();
        compareFontAndHeight(formFieldData, title);
      title = "Licensee Disclosure RPCD";
        formFieldData = licenseeDisclosureRPCD();
        compareFontAndHeight(formFieldData, title);
      title = "Listing Agreement Amend or Exten";
        formFieldData = listingAgreementAmendorExtend();
        compareFontAndHeight(formFieldData, title);
      title = "Listing Agreement";
        formFieldData = listingAgreement();
        compareFontAndHeight(formFieldData, title);
      title = "Native American Home Loan";
        formFieldData = nativeAmericanHomeLoan();
        compareFontAndHeight(formFieldData, title);
      title = "New Home Construction";
        formFieldData = newHomeConstruction();
        compareFontAndHeight(formFieldData, title);
      title = "Notice of Cancellation";
        formFieldData = noticeofCancellation();
        compareFontAndHeight(formFieldData, title);
      title = "Notice of TRR ";
        formFieldData = noticeofTRR();
        compareFontAndHeight(formFieldData, title);
      title = "Property Data Form";
        formFieldData = propertyDataForm();
        compareFontAndHeight(formFieldData, title);
      title = "RPCD Exemption Form";
        formFieldData = rPCDExemptionForm();
        compareFontAndHeight(formFieldData, title);
      title = "Release of Contract and Disbursement of Earnest Money";
        formFieldData = releaseofContractandDisbursementofEarnestMoney();
        compareFontAndHeight(formFieldData, title);
      title = "Residential Listing - Exclusive Right to Lease";
        formFieldData = residentialListingExclusiveRighttoLease();
        compareFontAndHeight(formFieldData, title);
      title = "Residential Property Disclosure Act 01- 2014";
        formFieldData = residentialPropertyDisclosureAct012014();
        compareFontAndHeight(formFieldData, title);
      title = "Residential Sale";
        formFieldData = residentialSale();
        compareFontAndHeight(formFieldData, title);
      title = "Sale of Buyers Property Condition - Not Under Contract";
        formFieldData = saleofBuyersPropertyConditionNotUnderContract();
        compareFontAndHeight(formFieldData, title);
      title = "Sellers Condition Removal Demand Notification";
        formFieldData = sellersConditionRemovalDemandNotification();
        compareFontAndHeight(formFieldData, title);
      title = "Sellers Counteroffer";
        formFieldData = sellersCounteroffer();
        compareFontAndHeight(formFieldData, title);
      title = "Single Family Homeowners Association";
        formFieldData = singleFamilyHomeownersAssociation();
        compareFontAndHeight(formFieldData, title);
      title = "Standard Clauses";
        formFieldData = standardClauses();
        compareFontAndHeight(formFieldData, title);
      title = "Supplement";
        formFieldData = supplement();
        compareFontAndHeight(formFieldData, title);
      title = "Townhouse Association";
        formFieldData = townhouseAssociation();
        compareFontAndHeight(formFieldData, title);
      title = "USDA Rural Housing Loan";
        formFieldData = uSDARuralHousingLoan();
        compareFontAndHeight(formFieldData, title);
      title = "VA Loan";
        formFieldData = vALoan();
        compareFontAndHeight(formFieldData, title);
      title = "Vacant Lot or Tract";
        formFieldData = vacantLotorTract();
        compareFontAndHeight(formFieldData, title);
      title = "Wire Advisory";
        formFieldData = wireAdvisory();
        compareFontAndHeight(formFieldData, title);

  //   title = "New Home Contract (Completed Construction)";
  //         formFieldData = newHomeContractCompletedConstruction();
  //         compareFontAndHeight(formFieldData, title);
  //   title = "Third Party Financing Addendum";
  //         formFieldData = addendumThirdPartyFinancing();
  //         compareFontAndHeight(formFieldData, title);
  //   title = "Environmental Assessment Addendum";
  //         formFieldData = addendumEnvironmentalAssessment();
  //   compareFontAndHeight(formFieldData, title);
  //    title = "Buyers Temporary Residential Lease";
  //         formFieldData = buyersTemporaryResidentialLease();
  //         compareFontAndHeight(formFieldData, title);
  //    title = "Landlord Floodplain and Flood Notice";
  //         formFieldData = landlordFloodplainandFloodNotice();
  //         compareFontAndHeight(formFieldData, title);
  //    title = "Loan Assumption Addendum";
  //         formFieldData = loanAssumptionAddendum();
  //         compareFontAndHeight(formFieldData, title);
  //   title = "Condominium Resale Certificate";
  //         formFieldData = condominiumResaleCertificate();
  //         compareFontAndHeight(formFieldData, title);
  // title = "Addendum for Coastal Area Property";
  //         formFieldData = addendumforCoastalAreaProperty();
  //         compareFontAndHeight(formFieldData, title);
  // title = "One to Four Family Residential Contract";
  //         formFieldData = onetoFourFamilyResidentialContract();
  //   compareFontAndHeight(formFieldData, title);
  //   title = "Farm and Ranch Contract";
  //         formFieldData = farmandRanchContract();
  //   compareFontAndHeight(formFieldData, title);
  //   title = "Non-Realty Items Addendum";
  //         formFieldData = nonRealtyItemsAddendum();
  //         compareFontAndHeight(formFieldData, title);
  //   title = "Notice of Buyers Termination";
  //         formFieldData = noticeofBuyersTermination();
  //         compareFontAndHeight(formFieldData, title);
  //   title = "Notice to Purchaser of Special Taxing District";
  //         formFieldData = noticetoPurchaserofSpecialTaxingDistrict();
  //   compareFontAndHeight(formFieldData, title);
  //   title = "Residential Condominium Contract";
  //         formFieldData = residentialCondominiumContract();
  //   compareFontAndHeight(formFieldData, title);
  //   title = "Sellers Temporary Residential Lease";
  //         formFieldData = sellersTemporaryResidentialLease();
  //         compareFontAndHeight(formFieldData, title);
  //         title = "Short Sale Addendum";
  //         formFieldData = addendumShortSale();
  //         compareFontAndHeight(formFieldData, title);

  //   title = "Subdivision and Property Owners Association";
  //         formFieldData = subdivisionandPropertyOwnersAssociation();
  //         compareFontAndHeight(formFieldData, title);
  //    title = "Unimproved Property Contract";
  //         formFieldData = unimprovedPropertyContract();
  //         compareFontAndHeight(formFieldData, title);

  // Alpha order except CB/ReMax up front
  // *** COLDWELL BANKER *** Affiliated, Mold, Radon, Wire, Personal xact
  title = "CB Colorado Affiliated Business Disclosure";
  formFieldData = cBColoradoAffiliatedBusinessDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "CB Exclusive Right to Buy 2023";
  formFieldData = cBExclusiveRighttoBuy2023();
  compareFontAndHeight(formFieldData, title);
  title = "CBC Exclusive Right to Buy 2023";
  formFieldData = cBCExclusiveRighttoBuy2023();
  compareFontAndHeight(formFieldData, title);
  title = "CB Exclusive Right to Sell 2023";
  formFieldData = cBExclusiveRighttoSell2023();
  compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Sell 2024 CB";
  formFieldData = exclusiveRighttoSell2024CB();
  compareFontAndHeight(formFieldData, title);
  title = "CBC Exclusive Right to Sell 2023";
  formFieldData = cBCExclusiveRighttoSell2023();
  compareFontAndHeight(formFieldData, title);
  title = "CB Mold Disclosure";
  formFieldData = cBMoldDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "CB Sales Associate Election Personal Transactions";
  formFieldData = cBSalesAssociateElectionPersonalTransactions();
  compareFontAndHeight(formFieldData, title);
  // title = "CB TRID Addendum";
  //     formFieldData = cBTRIDAddendum();
  // compareFontAndHeight(formFieldData, title)
  title = "CB Radon Disclosure";
  formFieldData = cBRadonDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "CB Wire Fraud Disclosure";
  formFieldData = cBWireFraudDisclosure();
  compareFontAndHeight(formFieldData, title);

  // *** Coldwell Banker Distinctive Properties
  title = "CBDP Affiliated Businesses 2022";
  formFieldData = cBDPAffiliatedBusinesses2022();
  compareFontAndHeight(formFieldData, title);
  title = "Affiliated Business Disclosure CBDP";
  formFieldData = affiliatedBusinessDisclosureCBDP();
  compareFontAndHeight(formFieldData, title);
  title = "Home Warranty AHS 2023";
  formFieldData = homeWarrantyAHS2023();
  compareFontAndHeight(formFieldData, title);

  // *** HomeSmart ***
  title = "Affiliate Business Arrangement HSPR";
  formFieldData = affiliateBusinessArrangementHSPR();
  compareFontAndHeight(formFieldData, title);
  title = "FSBO Commission Agreement HSPR";
  formFieldData = fSBOCommissionAgreementHSPR();
  compareFontAndHeight(formFieldData, title);
  title = "Home Warranty & Inspection Form";
  formFieldData = homeWarrantyInspectionFormHSPR();
  compareFontAndHeight(formFieldData, title);
  title = "Listing Termination Agreement HSPR";
  formFieldData = listingTerminationAgreementHSPR();
  compareFontAndHeight(formFieldData, title);
  title = "Referral Agreement Originating Agent HSPR";
  formFieldData = referralAgreementOriginatingAgentHSPR();
  compareFontAndHeight(formFieldData, title);
  title = "Referral Agreement Receiving Agent HSPR";
  formFieldData = referralAgreementReceivingAgentHSPR();
  compareFontAndHeight(formFieldData, title);

  // *** RE/MAX ***

  title = "REMAX Wire Fraud Alert";
  formFieldData = rEMAXWireFraudAlert();
  compareFontAndHeight(formFieldData, title);

  // *** REDDBOW ***
  title = "RB Commissions Disclosure 1023";
  formFieldData = rBCommissionsDisclosure1023();
  compareFontAndHeight(formFieldData, title);
  title = "RB Broker Rebate to Buyer 2022";
  formFieldData = rBBrokerRebatetoBuyer2022();
  compareFontAndHeight(formFieldData, title);
  title = "RB Home Inspection Waiver Agreement23";
  formFieldData = rBHomeInspectionWaiverAgreement23();
  compareFontAndHeight(formFieldData, title);
  title = "RB Seller Coming Soon Addendum 2022";
  formFieldData = rBSellerComingSoonAddendum2022();
  compareFontAndHeight(formFieldData, title);

  // *** Extra bonus forms Colorado ***
  title = "Addendum Blank";
  formFieldData = addendumBlank();
  compareFontAndHeight(formFieldData, title);
  // *** Colorado Forms ***
  title = "Agreement to Revive";
  formFieldData = agreementtoRevive();
  compareFontAndHeight(formFieldData, title);
  title = "Amend-Extend Contract";
  formFieldData = amendExtendContract();
  compareFontAndHeight(formFieldData, title);
  title = "Amend-Extend Contract 2023";
  formFieldData = amendExtendContract2023();
  compareFontAndHeight(formFieldData, title);
  title = "Amend-Extend Without Dates";
  formFieldData = amendExtendWithoutDates();
  compareFontAndHeight(formFieldData, title);
  title = "Appraisal Value Objection Notice";
  formFieldData = appraisalValueObjectionNotice();
  compareFontAndHeight(formFieldData, title);
  title = "Assignment of Lease";
  formFieldData = assignmentofLease();
  compareFontAndHeight(formFieldData, title);
  title = "Bill of Sale (Contract to Buy and Sell)";
  formFieldData = billofSaleContract();
  compareFontAndHeight(formFieldData, title);
  title = "Bill of Sale (PPA)";
  formFieldData = billofSalePPA();
  compareFontAndHeight(formFieldData, title);
  title = "Brokerage Disclosure to Buyer";
  formFieldData = brokerageDisclosuretoBuyer();
  compareFontAndHeight(formFieldData, title);
  title = "Brokerage Disclosure to Seller (FSBO)"; //FSBO
  formFieldData = brokerageDisclosuretoSellerFSBO();
  compareFontAndHeight(formFieldData, title);
  title = "Brokerage Duties Disclosure to Seller (non-CREC Listing)";
  formFieldData = brokerageDutiesDisclosuretoSellernonCRECListing();
  compareFontAndHeight(formFieldData, title);
  title = "Buyer Contract Amend-Extend 2023";
  formFieldData = buyerContractAmendExtend2023();
  compareFontAndHeight(formFieldData, title);
  title = "Change of Status";
  formFieldData = changeofStatus();
  compareFontAndHeight(formFieldData, title);
  title = "Closing Instructions";
  formFieldData = closingInstructions();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell (Commercial)";
  formFieldData = contracttoBuyandSellCommercial();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Foreclosure 2023";
  formFieldData = contracttoBuyandSellForeclosure2023();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Land";
  formFieldData = contracttoBuyandSellLand();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Land 2024";
  formFieldData = contracttoBuyandSellLand2024();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Income Residential";
  formFieldData = contracttoBuyandSellIncome();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Income Residential 2023";
  formFieldData = contracttoBuyandSellIncomeResidential2023();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Residential";
  formFieldData = contracttoBuyandSellResidential();
  compareFontAndHeight(formFieldData, title);
  title = "Contract to Buy and Sell, Residential 2023";
  formFieldData = contracttoBuyandSellResidential2023();
  compareFontAndHeight(formFieldData, title);
  title = "Counterproposal";
  formFieldData = counterproposal();
  compareFontAndHeight(formFieldData, title);
  title = "Counterproposal 2023";
  formFieldData = counterproposal2023();
  compareFontAndHeight(formFieldData, title);
  title = "Counterproposal Without Dates";
  formFieldData = counterproposalWithoutDates();
  compareFontAndHeight(formFieldData, title);
  title = "Definitions of Working Relationships (for Sellers)";
  formFieldData = definitionsofWorkingRelationshipsForSellers();
  compareFontAndHeight(formFieldData, title);
  title = "Definitions of Working Relationships (for Buyers)";
  formFieldData = definitionsofWorkingRelationshipsForBuyers();
  compareFontAndHeight(formFieldData, title);
  title = "Earnest Money Release";
  formFieldData = earnestMoneyRelease();
  compareFontAndHeight(formFieldData, title);
  title = "Earnest Money Receipt";
  formFieldData = earnestMoneyReceipt();
  compareFontAndHeight(formFieldData, title);
  title = "Energy Benchmarking Disclosure";
  formFieldData = energyBenchmarkingDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "Estoppel Statement";
  formFieldData = estoppelStatement();
  compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Buy";
  formFieldData = exclusiveRighttoBuy();
  compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Buy 2023";
  formFieldData = exclusiveRighttoBuy2023();
  compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Sell";
  formFieldData = exclusiveRighttoSell();
  compareFontAndHeight(formFieldData, title);
  title = "Exclusive Right to Sell 2024";
  formFieldData = exclusiveRighttoSell2024();
  compareFontAndHeight(formFieldData, title);
  title = "Extension or Termination";
  formFieldData = extensionorTermination();
  compareFontAndHeight(formFieldData, title);
  title = "Exchange Addendum to Contract to Buy and Sell Real Estate";
  formFieldData = exchangeAddendumtoContracttoBuySellRealEstate();
  compareFontAndHeight(formFieldData, title);
  title = "Green Disclosure";
  formFieldData = greenDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "Inspection Objection Notice";
  formFieldData = inspectionObjectionNotice();
  compareFontAndHeight(formFieldData, title);
  title = "Inspection Objection Notice 2023";
  formFieldData = inspectionObjectionNotice2023();
  compareFontAndHeight(formFieldData, title);
  title = "Inspection Resolution";
  formFieldData = inspectionResolution();
  compareFontAndHeight(formFieldData, title);
  title = "Inspection Resolution 2023";
  formFieldData = inspectionResolution2023();
  compareFontAndHeight(formFieldData, title);
  title = "Lead Based Paint Disclosure (Sales)";
  formFieldData = leadBasedPaintDisclosureSalesWritable1();
  compareFontAndHeight(formFieldData, title);
  title = "Listing Contract Amend-Extend";
  formFieldData = listingContractAmendExtendCleanWritable2020();
  compareFontAndHeight(formFieldData, title);
  title = "Listing Contract Amend-Extend 2023";
  formFieldData = listingContractAmendExtend2023();
  compareFontAndHeight(formFieldData, title);
  title = "Manufactured Home Addendum 2024";
  formFieldData = manufacturedHomeAddendum2024();
  compareFontAndHeight(formFieldData, title);
  title = "Manufactured Home Amend Extend 2024";
  formFieldData = manufacturedHomeAmendExtend2024();
  compareFontAndHeight(formFieldData, title);
  title = "Manufactured Home CBS Lot Lease 2024";
  formFieldData = manufacturedHomeCBSLotLease2024();
  compareFontAndHeight(formFieldData, title);
  title = "Manufactured Home Counterproposal 2024";
  formFieldData = manufacturedHomeCounterproposal2024();
  compareFontAndHeight(formFieldData, title);
  title = "Notice to Terminate";
  formFieldData = noticeToTerminate();
  compareFontAndHeight(formFieldData, title);
  title = "Notice to Terminate (Buyer) 2024";
  formFieldData = noticetoTerminateBuyer();
  compareFontAndHeight(formFieldData, title);
  title = "Notice to Terminate (Seller) 2024";
  formFieldData = noticetoTerminateSeller();
  compareFontAndHeight(formFieldData, title);
  title = "Open Listing Addendum to Listing Contract";
  formFieldData = openListingAddendumtoListingContract();
  compareFontAndHeight(formFieldData, title);
  title = "Personal Property Agreement";
  formFieldData = personalPropertyAgreement();
  compareFontAndHeight(formFieldData, title);
  title = "Post-Closing Occupancy Agreement";
  formFieldData = postClosingOccupancyAgreement();
  compareFontAndHeight(formFieldData, title);
  title = "Residential Addendum to Contract";
  formFieldData = residentialAddendumtoContracttoBuySellRealEstate();
  compareFontAndHeight(formFieldData, title);
  title = "Residential Addendum to Contract 2024";
  formFieldData = residentialAddendumtoContract2024();
  compareFontAndHeight(formFieldData, title);
  title = "Sellers Property Disclosures (Land) 2024";
  formFieldData = sellersPropertyDisclosuresLand2024();
  compareFontAndHeight(formFieldData, title);
  title = "Sellers Property Disclosure (Land)";
  formFieldData = sellersPropertyDisclosureLand();
  compareFontAndHeight(formFieldData, title);
  title = "Sellers Property Disclosure (Residential)";
  formFieldData = sellersPropertyDisclosureResidential();
  compareFontAndHeight(formFieldData, title);
  title = "Sellers Property Disclosure (Residential) 2023";
  formFieldData = sellersPropertyDisclosureResidential2023();
  compareFontAndHeight(formFieldData, title);
  title = "Sellers Property Disclosure Supplement (Additional Structure)";
  formFieldData = sellersPropertyDisclosureSupplementAdditionalStructure();
  compareFontAndHeight(formFieldData, title);
  title = "Short Sale Addendum (CBS)";
  formFieldData = shortSaleAddendumCBS();
  compareFontAndHeight(formFieldData, title);
  title = "Short Sale Addendum (Seller Listing Contract)";
  formFieldData = shortSaleAddendumSellerListingContract();
  compareFontAndHeight(formFieldData, title);
  title = "Square Footage Disclosure";
  formFieldData = squareFootageDisclosure();
  compareFontAndHeight(formFieldData, title);
  title = "Source of Water Addendum";
  formFieldData = sourceOfWaterAddendum();
  compareFontAndHeight(formFieldData, title);
  title = "Title-Related Objection Notice";
  formFieldData = titleRelatedObjectionNotice();
  compareFontAndHeight(formFieldData, title);

  title = "cOVID19Addendum2020";
  formFieldData = cOVID19Addendum2020();
  compareFontAndHeight(formFieldData, title);

  return;
}
